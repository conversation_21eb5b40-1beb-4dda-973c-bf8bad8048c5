import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';

class MarketLimitToggler extends StatelessWidget {
  final bool isMarket;
  final VoidCallback onMarketTap;
  final VoidCallback onLimitTap;

  const MarketLimitToggler({
    Key? key,
    required this.isMarket,
    required this.onMarketTap,
    required this.onLimitTap,
  }) : super(key: key);

  Widget _buildToggleButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required bool isDarkMode,
  }) {
    return SizedBox(
      height: 40,
      width: 100,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelected 
                ? AppTheme.primaryColor(isDarkMode)
                : AppTheme.cardColor(isDarkMode),
            borderRadius: BorderRadius.circular(50),
            border: Border.all(
              color: AppTheme.borderColor(isDarkMode),
              width: 1,
            ),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: isSelected 
                  ? Colors.white
                  : AppTheme.textPrimary(isDarkMode),
              fontWeight: FontWeight.w600,
              fontSize: 20,
            ),
          ),
        ),
      ),
    );
  }
  

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          height: 58,
          width: 380,
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: themeState.isDarkMode 
                    ? Colors.black26 
                    : Colors.grey.withOpacity(0.2),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            spacing: 13,
            children: [
              const SizedBox(width: 1),
              _buildToggleButton(
                label: "Market",
                isSelected: isMarket,
                onTap: onMarketTap,
                isDarkMode: themeState.isDarkMode,
              ),
              _buildToggleButton(
                label: "Limit",
                isSelected: !isMarket,
                onTap: onLimitTap,
                isDarkMode: themeState.isDarkMode,
              ),
            ],
          ),
        );
      },
    );
  }
}
