import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/bottom_navigation/bloc/bottom_navigation_bloc.dart';
import 'package:phoenix/features/orders/bloc/orders_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/watchlist/bloc/watchist_bloc.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/screens/orders/order_form_bottom_sheet.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/screens/orders/orders_screen.dart';
import 'package:phoenix/screens/spider/spider_page.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/screens/portfolio/portfolio_screen.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/app_bar/custom_app_bar.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/home/<USER>';
import 'package:phoenix/widgets/toast/custom_toast.dart';
import 'package:zen_navbar/zen_navbar.dart';
import 'features/orders_state/bloc/orders_state_bloc.dart';
import 'features/pnl/bloc/pnl_bloc.dart';
import 'features/portfolio_data/bloc/portfolio_bloc.dart';
import 'widgets/animated_order_button.dart';
import 'widgets/strategy_fab.dart';
import 'package:phoenix/screens/watchlist/watchlist_screen.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

//this is home with app bar, side menu, bottom nav bar center button
class _HomeState extends State<Home> with TickerProviderStateMixin {
  late final AnimationController _formSheetAnimeController;

  @override
  void initState() {
    super.initState();
    _formSheetAnimeController = BottomSheet.createAnimationController(this);
    _formSheetAnimeController.duration = const Duration(milliseconds: 850);
  }

  final List<Widget> screens = const [
    PortfolioScreen(),
    WatchlistScreen(),
    OrdersScreen(),
    SpiderPage(),
  ];

  Future<bool> _onWillPop(BuildContext context, int currentIndex) async {
    if (currentIndex != 0) {
      context.read<BottomNavigationBloc>().add(
            BottomNavigationChangeEvent(currentPageIndex: 0),
          );
      return false;
    } else {
      return await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text("Exit App"),
              content: const Text("Are you sure you want to exit the app?"),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text("No"),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text("Yes"),
                ),
              ],
            ),
          ) ??
          false;
    }
  }

  void _handleOrderState(
    BuildContext context,
    OrdersState state,
  ) {
    if (state is OrderPlaced) {
      if (state.status == "ACCEPTED") {
        if (state.methodType == "PUT") {
          ZenSnackbar.show(
            context,
            'Modify in progress',
            type: ToastType.info,
          );
        } else if (state.methodType == "DELETE") {
          ZenSnackbar.show(
            context,
            'Delete in progress',
            type: ToastType.info,
          );
        } else {
          ZenSnackbar.show(
            context,
            'Accepted',
            type: ToastType.success,
          );
        }
      } else if (state.status == "REJECTED") {
        if (state.methodType == "PUT") {
          ZenSnackbar.show(
            context,
            'Modifiy order rejected',
            type: ToastType.error,
          );
        } else if (state.methodType == "DELETE") {
          ZenSnackbar.show(
            context,
            'Delete in progress',
            type: ToastType.error,
          );
        } else {
          ZenSnackbar.show(
            context,
            state.status.toCapitalized,
            type: ToastType.error,
          );
        }
      } else {
        ZenSnackbar.show(
          context,
          state.status.toCapitalized,
          type: ToastType.warning,
        );
      }

      final authState = context.read<AuthBloc>().state; // Get current state
      if (authState is AuthAuthenticated) {
        BlocProvider.of<OrdersStateBloc>(context).add(FetchOrdersState(
          clientId: authState.credentialsModel.clientId,
        ));
      } else {
        // Handle unauthenticated case (e.g., show login dialog)
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("You need to log in first")),
        );
      }
      //Resetting to initial event
      BlocProvider.of<OrdersBloc>(context).add(OrdersInitializeEvent());
    }

    if (state is OrderLoading) {
      hideOrderForm(context);
    }

    if (state is OrderError) {
      ZenSnackbar.show(
        context,
        state.message.toCapitalized,
        type: ToastType.error,
      );

      //Resetting to initial event
      BlocProvider.of<OrdersBloc>(context).add(OrdersInitializeEvent());
    }
  }

  // State variable to track if the buy/sell buttons are visible
  bool _isBuySellVisible = false;

  void _toggleBuySellButtons() {
    setState(() {
      _isBuySellVisible = !_isBuySellVisible;
      print('Center button tapped. _isBuySellVisible: $_isBuySellVisible');
    });
  }

  Future<void> _showOrderForm(FormOpenOrderType type, int pageIndex) async {
    setState(() {
      _isBuySellVisible = false;
    });

    // Store the current context's mounted status
    final isStillMounted = mounted;

    await Future.delayed(const Duration(milliseconds: 300));

    if (!isStillMounted) return;

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      transitionAnimationController: _formSheetAnimeController,
      builder: (modalContext) {
        return SingleChildScrollView(
          reverse: true,
          child: OrderFormSheet(openOrderType: type),
        );
      },
    ).whenComplete(() {
      debugPrint("$type form closed");

      if (mounted) {
        // Only access context when mounted
        BlocProvider.of<WebSocketBloc>(context).add(WebSocketSelectStock(null));

        if (pageIndex == 0) {
          handlePositionPnlSocketSubscriptions(context);
        } else if (pageIndex == 1) {
          handleWatchlistPageSocketSubscriptions(context);
        } else if (pageIndex == 2) {
          handleOrdersPageSocketSubscriptions(context);
        }
      }
    }).catchError((error) {
      debugPrint("Error in $type modal: $error");
    });
  }

  void handlePositionPnlSocketSubscriptions(BuildContext context) {
    final Set<int> zenSecIds = {};

    // From PnL Bloc
    final pnlState = context.read<PnlBloc>().state;
    if (pnlState is PnlLoaded) {
      zenSecIds.addAll(
        pnlState.pnlData.map((p) => p.positionCompositeKey.zenSecId),
      );
    }

    // From Portfolio Bloc
    final portfolioState = context.read<PortfolioBloc>().state;
    if (portfolioState is PortfolioLoaded) {
      zenSecIds.addAll(
        portfolioState.openPositions
            .map((p) => p.positionCompositeKey.zenSecId),
      );
      zenSecIds.addAll(
        portfolioState.closedPositions
            .map((p) => p.positionCompositeKey.zenSecId),
      );
    }

    debugPrint(
      "🔄 Updating WebSocket subscriptions for position & pnl - ${zenSecIds.length} unique stocks",
    );

    context.read<WebSocketBloc>().add(
          WebSocketSelectMultipleStocks(zenSecIds.toList()),
        );
  }

  void handleOrdersPageSocketSubscriptions(BuildContext context) {
    final ordersState = context.read<OrdersStateBloc>().state;
    if (ordersState is OrdersStateLoaded) {
      final openData = ordersState.data
          .where((order) => [
                "update",
                "trigger_pending",
                "open",
                "in_progress",
                "pending"
              ].contains(order.status.toLowerCase()))
          .toList();
      Set<int> stockIds =
          openData.map((e) => e.positionCompKey.zenSecId).toSet();
      debugPrint(
        "🔄 Updating WebSocket subscriptions for open orders - ${stockIds.length} stocks",
      );
      context
          .read<WebSocketBloc>()
          .add(WebSocketSelectMultipleStocks(stockIds.toList()));
    }
  }

  void handleWatchlistPageSocketSubscriptions(BuildContext context) {
    final wl = context.read<WatchistBloc>().state;
    if (wl is WatchlistLoaded) {
      final stockIds =
          wl.watchlistData[0].securities.map((wl) => wl.zenId).toSet().toList();
      debugPrint(
        "🔄 Updating WebSocket subscriptions for WL - ${stockIds.length} stocks",
      );
      context
          .read<WebSocketBloc>()
          .add(WebSocketSelectMultipleStocks(stockIds));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(builder: (context, themeState) {
      return BlocBuilder<SecurityListBloc, SecurityListState>(
        builder: (context, _) {
          return BlocBuilder<BottomNavigationBloc, BottomNavigationState>(
            builder: (context, navState) {
              final currentIndex = (navState is BottomNavigaionChanged)
                  ? navState.currentPageIndex
                  : 0;

              final bool isSpiderScreen = currentIndex == 3;

              final List<NavItem> navItems = [
                NavItem(
                  icon: "images/nav-bar/portfolio_icon.png",
                  label: "Portfolio",
                  screen: screens[0],
                ),
                NavItem(
                  icon:
                      "images/nav-bar/watchlist_icon.png", // Update this icon as needed
                  label: "Watchlist",
                  screen: screens[1],
                ),
                NavItem(
                  icon: "images/nav-bar/orders_icon.png",
                  label: "Orders",
                  screen: screens[2],
                ),
                NavItem(
                  icon: "images/nav-bar/graph_icon.png",
                  label: "Spider",
                  screen: screens[3],
                ),
              ];

              ///This is to listen to the order state and show the toast message
              return BlocConsumer<OrdersBloc, OrdersState>(
                listener: _handleOrderState,
                builder: (context, state) {
                  return WillPopScope(
                    onWillPop: () => _onWillPop(context, currentIndex),
                    child: Stack(
                      children: [
                        Scaffold(
                          backgroundColor: isSpiderScreen
                              ? Colors.transparent
                              : AppTheme.backgroundColor(themeState.isDarkMode),
                          drawer: const SideDrawer(),
                          appBar: isSpiderScreen
                              ? null
                              : CustomAppBar(
                                  onClientSwitched: (clientId) {
                                    switch (currentIndex) {
                                      case 0:
                                        context
                                            .read<PortfolioBloc>()
                                            .add(FetchPortfolio(clientId));
                                        context
                                            .read<PnlBloc>()
                                            .add(FetchPnlData(clientId));
                                        break;
                                      case 1:
                                        context
                                            .read<PnlBloc>()
                                            .add(FetchPnlData(clientId));
                                        break;
                                      case 2:
                                        context.read<OrdersStateBloc>().add(
                                            FetchOrdersState(
                                                clientId: clientId));
                                        break;
                                      case 3:
                                        break;
                                    }
                                  },
                                ),
                          body: navItems[currentIndex].screen,
                          bottomNavigationBar: ZenNavBar(
                            items: navItems,
                            backgroundColor:
                                AppTheme.backgroundColor(themeState.isDarkMode),
                            selectedItemColor:
                                AppTheme.primaryColor(themeState.isDarkMode),
                            unselectedItemColor:
                                AppTheme.textPrimary(themeState.isDarkMode),
                            centerButton: AnimatedSwitcher(
                              duration: const Duration(milliseconds: 700),
                              transitionBuilder: (child, animation) =>
                                  FadeTransition(
                                opacity: animation,
                                child: ScaleTransition(
                                    scale: animation, child: child),
                              ),
                              child: Container(
                                key: ValueKey(_isBuySellVisible),
                                height: 60,
                                width: 60,
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0xFF2D6FE6)
                                          .withOpacity(0.4),
                                      blurRadius: 15,
                                      spreadRadius: 0,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                  image: DecorationImage(
                                    image: AssetImage(_isBuySellVisible
                                        ? 'images/floating-cross-button.png'
                                        : 'images/plus_button.png'),
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                            onCenterButtonTap: _toggleBuySellButtons,
                            onItemTap: (int index) {
                              context.read<BottomNavigationBloc>().add(
                                  BottomNavigationChangeEvent(
                                      currentPageIndex: index));
                            },
                          ),
                          floatingActionButton: (!isSpiderScreen)
                              ? Stack(
                                  alignment: Alignment.bottomRight,
                                  children: [
                                    // Strategy FAB (no animation)
                                    BlocBuilder<BottomNavigationBloc,
                                        BottomNavigationState>(
                                      builder: (context, state) {
                                        final isNetWorthBarExpanded =
                                            (state is BottomNavigaionChanged)
                                                ? state.isNetWorthBarExpanded
                                                : false;

                                        return Padding(
                                          padding: EdgeInsets.only(
                                              bottom: 16, right: 4),
                                          child: StrategyFAB(
                                            isNetWorthBarExpanded:
                                                isNetWorthBarExpanded,
                                          ),
                                        );
                                      },
                                    ),
                                    // Buy/Sell buttons that appear when the center button is tapped
                                    if (_isBuySellVisible)
                                      AnimatedSwitcher(
                                        duration:
                                            const Duration(milliseconds: 300),
                                        transitionBuilder: (child, animation) =>
                                            FadeTransition(
                                          opacity: animation,
                                          child: SlideTransition(
                                            position: Tween<Offset>(
                                              begin: const Offset(1.0, 0.0),
                                              end: Offset.zero,
                                            ).animate(animation),
                                            child: child,
                                          ),
                                        ),
                                        child: Align(
                                          key: const ValueKey('buySell'),
                                          alignment: Alignment.bottomRight,
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              const SizedBox(height: 16),
                                              AnimatedOrderButton(
                                                label: 'Sell',
                                                color: ThemeConstants
                                                    .sellSlideOptionColor,
                                                onTap: () => _showOrderForm(
                                                  FormOpenOrderType.sell,
                                                  currentIndex,
                                                ),
                                                delay: 600,
                                              ),
                                              const SizedBox(height: 16),
                                              AnimatedOrderButton(
                                                label: 'Buy',
                                                color: ThemeConstants
                                                    .buySlideOptionColor,
                                                onTap: () => _showOrderForm(
                                                  FormOpenOrderType.buy,
                                                  currentIndex,
                                                ),
                                                delay: 500,
                                              ),
                                              const SizedBox(
                                                height: 90,
                                              ), // space above the FAB
                                            ],
                                          ),
                                        ),
                                      ),
                                  ],
                                )
                              : (_isBuySellVisible
                                  ? AnimatedSwitcher(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      transitionBuilder: (child, animation) =>
                                          FadeTransition(
                                        opacity: animation,
                                        child: SlideTransition(
                                          position: Tween<Offset>(
                                            begin: const Offset(1.0, 0.0),
                                            end: Offset.zero,
                                          ).animate(animation),
                                          child: child,
                                        ),
                                      ),
                                      child: Align(
                                        key: const ValueKey('buySellSpider'),
                                        alignment: Alignment.bottomRight,
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            const SizedBox(height: 16),
                                            AnimatedOrderButton(
                                              label: 'Sell',
                                              color: ThemeConstants
                                                  .sellSlideOptionColor,
                                              onTap: () => _showOrderForm(
                                                FormOpenOrderType.sell,
                                                currentIndex,
                                              ),
                                              delay: 200,
                                            ),
                                            const SizedBox(height: 16),
                                            AnimatedOrderButton(
                                              label: 'Buy',
                                              color: ThemeConstants
                                                  .buySlideOptionColor,
                                              onTap: () => _showOrderForm(
                                                FormOpenOrderType.buy,
                                                currentIndex,
                                              ),
                                              delay: 100,
                                            ),
                                            const SizedBox(height: 90),
                                          ],
                                        ),
                                      ),
                                    )
                                  : null),
                        ),
                        if (state is OrderLoading)
                          Positioned.fill(
                            child: Container(
                              color: Colors.black.withOpacity(0.4),
                              child: Center(
                                child: CircularLoader(),
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              );
            },
          );
        },
      );
    });
  }
}
