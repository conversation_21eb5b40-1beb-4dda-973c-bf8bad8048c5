name: phoenix
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  sentry_flutter: ^8.14.2
  flutter:
    sdk: flutter
  zen_spider:
    git:
      url: https://oauth2:<EMAIL>/ui-app1/spider.git
      ref: main
  zen_navbar:
    git:
      url: https://oauth2:<EMAIL>/ui-app1/zen_navbar.git
      ref: main

  cupertino_icons: ^1.0.8
  auth0_flutter: ^1.7.2
  flutter_secure_storage: ^9.0.0
  flutter_inner_shadow: ^0.0.1
  web_socket_channel: 2.4.0
  ntp: ^2.0.0
  provider: ^6.1.2
  flutter_bloc: ^8.1.6
  shared_preferences: ^2.3.4
  flutter_inset_box_shadow: ^1.0.8
  http: ^1.2.2
  dropdown_button2: ^2.3.9
  protoc_plugin: ^21.1.2
  protobuf: ^3.1.0
  intl: ^0.18.1
  bloc: ^8.1.4
  local_auth: ^2.1.8
  local_auth_android: ^1.0.36
  local_auth_ios: ^1.1.5
  webview_flutter: ^4.2.2
  fl_chart: ^0.63.0
  path_provider: ^2.1.2
  share_plus: ^7.2.1
  fuzzy: ^0.5.1

dev_dependencies:
  sentry_dart_plugin: ^2.4.1
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
   - images/
   - images/nav-bar/
   - images/tile-generic/
   - assets/data.json
   - assets/data2.json
   - images/general/
   - images/arrows/
   - images/brokers/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  project: flutter
  org: zentropy-technologies
