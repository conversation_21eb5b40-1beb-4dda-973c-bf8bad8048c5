///All the endpoints will be managed here
abstract class ApiPath {
  static const String devHost =
      "https://thor.intraday.dev.phoenix.zentropylabs.com";
  static const String uatHost =
      "https://thor.intraday.uat.phoenix.zentropylabs.com";
  static const String prodHost =
      "https://thor.intraday.phoenix.zentropylabs.com";
  static const String prodStrategyHost =
      "https://api.strategy.phoenix.zentropylabs.com";
  static const String uatStrategyHost =
      "https://api.uat.strategy.phoenix.zentropylabs.com";
  static const String devStrategyHost =
      "https://api.dev.strategy.phoenix.zentropylabs.com";
  static const String prodHostIP = "http://*************:9190";
  static const String uatHostIP = "http://*************:9190";
  static const String currentHost = prodHost;
  static const String currentStrategyHost = prodStrategyHost;

  static String getClients() {
    return "$currentHost/v1/clients";
  }

  static String getClientInfo(String id) {
    return Uri.encodeFull("$currentHost/v1/$id/client-info");
  }

  static String getMargins(int clientId, int accountId) {
    return Uri.encodeFull(
        "$currentHost/v1/$clientId/margins?account_id=$accountId");
  }

  static String getPortfolioBeta(int clientId) {
    return Uri.encodeFull("$currentHost/v1/$clientId/trade/portfolio-beta");
  }

  ///Need to change this to registerUser
  ///
  static String registerClient() {
    return Uri.encodeFull("$currentHost/v1/register-client");
  }

  static String registerUser() {
    return "$currentHost/v1/register-user";
  }

  static String userDetail() {
    return "$currentHost/v1/user-detail";
  }

  static String registerBrokerAccount(int clientId) {
    return "$currentHost/v1/$clientId/register-broker-account";
  }

  static String registerStrategy(int clientId) {
    return "$currentHost/v1/$clientId/register-strategy";
  }

  static String getPositions(int clientId) {
    return "$currentHost/v1/$clientId/trade/position";
  }

  static String getOpenColosedPositions(int clientId) {
    return "$currentHost/v2/$clientId/trade/position";
  }

  static String getPnL(int clientId) {
    return "$currentHost/v2/$clientId/trade/pnl";
  }

  static String placeOrder(int clientId) {
    return "$currentHost/v1/$clientId/order/place-order";
  }

  static String getZenOrderState(int clientId) {
    return "$currentHost/v1/$clientId/order/zen-order-state";
  }

  static String getSentinelOrderState(int clientId) {
    return "$currentHost/v1/$clientId/order/sentinel-order-state";
  }

  static String getUnifiedOrderState(int clientId) {
    return "$currentHost/v1/$clientId/order/unified-order-state";
  }

  //charges - commisions 
  static String getAllCharges() {
    return "$currentHost/v1/all-charges";
  }

  //securities list
  static String getAllSourceSecurities() {
    return "$currentHost/v1/all-source-securities";
  }

  //trabale securities
  static String getTradableSecurities() {
    return "$currentHost/v1/tradable-securities";
  }

  ///Latest order state by zenId
  static String getLatestOrder(int zenOrderId, int clientId) {
    return "$currentHost/v1/$clientId/order/latest-order?zen_order_id=$zenOrderId";
  }

  //Get latest price for a stock
  static String getLtpPrice(int zenId) {
    return "$currentHost/v1/latest-price?zen_id=$zenId";
  }

  //Get SOD price for multiple stocks
  static String getSodPrice() {
    return "$currentHost/v1/sod-price";
  }

  //Strategy urls
  static Uri strategyChat() {
    return Uri.parse("$currentStrategyHost/api/v0/strategy/chat");
  }

  static Uri strategySave() {
    return Uri.parse("$currentStrategyHost/api/v0/strategy/save");
  }

  static Uri refreshOldStrategy() {
    return Uri.parse('$currentStrategyHost/api/v0/strategy/refresh');
  }

  static Uri strategyChatHistory(userId) {
    return Uri.parse(
        '$currentStrategyHost/api/v0/strategy/chat-history');
  }

  static String getTrades(int clientId) {
    return "$currentHost/v1/$clientId/trade/trades";
  }

  // Option Greeks WebSocket URLs
  static const String optionGreeksWebSocketDev = "wss://websocket.dev.phoenix.zentropylabs.com/ws/zen_option_greeks";
  static const String optionGreeksWebSocketProd = "wss://websocket.phoenix.zentropylabs.com/ws/zen_option_greeks";
  static const String optionGreeksWebSocketUat = "wss://websocket.uat.phoenix.zentropylabs.com/ws/zen_option_greeks";
}
